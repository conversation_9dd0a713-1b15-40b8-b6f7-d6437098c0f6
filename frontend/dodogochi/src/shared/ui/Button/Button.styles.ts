import styled from 'styled-components';

export const ButtonStyled = styled.button`
	padding: 17px 40px;
	width: 100%;
	border: 0;
	border-radius: 24px;
	background-color: rgb(41, 172, 255);
	box-shadow: rgb(29, 133, 197) 0px 10px 0px 0px;
	color: hsl(0, 0%, 100%);
	letter-spacing: 1.5px;
	font-weight: 800;
	font-size: 16px;
	cursor: pointer;
	transition: all 0.3s ease;

	&:hover {
		box-shadow: rgb(28, 156, 237) 0px 7px 0px 0px;
	}

	&:active {
		background-color: rgb(41, 172, 255);
		/*50, 168, 80*/
		box-shadow: rgb(26, 126, 188) 0px 0px 0px 0px;
		transition: 200ms;
		transform: translateY(5px);
	}
`;
