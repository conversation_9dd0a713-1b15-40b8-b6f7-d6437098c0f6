import { type FC, StrictMode } from 'react';
import { LoggerContextProvider } from '@dodobrands/react-logger';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { createHashHistory, createRouter, RouterProvider } from '@tanstack/react-router';

import { routeTree } from 'app/routes.gen';
import { GlobalStyles } from 'shared/assets/styles/GlobalStyles';
import { logger } from 'shared/lib/logger/logger';

const queryClient = new QueryClient();

const hashHistory = createHashHistory();

const router = createRouter({
	routeTree,
	context: {
		queryClient,
	},
	defaultPreload: 'intent',
	defaultPreloadStaleTime: 0,
	scrollRestoration: true,
	history: hashHistory,
});

declare module '@tanstack/react-router' {
	interface Register {
		router: typeof router;
	}
}

export const App: FC = () => (
	<StrictMode>
		<LoggerContextProvider loggerInstanceRef={logger}>
			<QueryClientProvider client={queryClient}>
				<GlobalStyles />
				<RouterProvider router={router} />
				{/* <ReactQueryDevtools /> */}
			</QueryClientProvider>
		</LoggerContextProvider>
	</StrictMode>
);
