import { type FC } from 'react';

import {
	EnergyEmojiStyled,
	PointsStyled,
	WrapperStyled,
} from 'widgets/UserStatistics/components/EnergyPoints/EnergyPoints.styles';

interface Props {
	points: number;
}

export const EnergyPoints: FC<Props> = ({ points }) => (
	<WrapperStyled>
		{/* eslint-disable-next-line i18next/no-literal-string */}
		<EnergyEmojiStyled>⚡</EnergyEmojiStyled>
		<PointsStyled>{points}</PointsStyled>
	</WrapperStyled>
);
