import { type FC } from 'react';

import {
	EmojiWrapperStyled,
	PercentageStyled,
	WrapperStyled,
} from 'widgets/UserStatistics/components/AdditionalStatisticsItem/AdditionalStatisticsItem.styles';

interface Props {
	count: number;
	emoji: string;
}

export const AdditionalStatisticsItem: FC<Props> = ({ count, emoji }) => (
	<WrapperStyled>
		<EmojiWrapperStyled>{emoji}</EmojiWrapperStyled>
		<PercentageStyled>{count}</PercentageStyled>
	</WrapperStyled>
);
