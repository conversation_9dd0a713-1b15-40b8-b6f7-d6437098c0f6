import styled from 'styled-components';

export const WrapperStyled = styled.div`
	backdrop-filter: blur(10px);
	gap: 24px;
	display: flex;
	flex-direction: column;
	padding: 24px;
	max-width: 1200px;
	border: 1px solid rgba(255, 255, 255, 0.1);
	border-radius: 36px;
	background: rgba(255, 255, 255, 0.1);
`;

export const CharacterWrapperStyled = styled.div`
	gap: 12px;
	display: flex;
	flex: 1;
	justify-content: space-between;
`;

export const CharacterSectionStyled = styled.div`
	gap: 12px;
	display: flex;
	flex: 1;
	flex-direction: column;
`;
export const CharacterImageWrapperStyled = styled.div`
	flex: 1;
	overflow: hidden;
	min-height: 100px;
	border-radius: 36px;
`;

export const CharacterImageStyled = styled.img`
	object-fit: cover;
	width: 150px;
	height: 150px;
`;

export const TotalScoreWrapperStyled = styled.div`
	display: flex;
	flex: 1;
	flex-direction: column;
	justify-content: center;
	align-items: center;
`;

export const AdditionalStatisticsWrapperStyled = styled.div`
	gap: 12px;
	display: flex;
	justify-content: center;
	align-items: center;
`;

export const AdditionalStatisticsItemWrapperStyled = styled.div`
	display: flex;
	flex: 1;
`;

export const GameButtonsWrapperStyled = styled.div`
	gap: 12px;
	display: flex;
`;

export const GameButtonWrapperStyled = styled.div`
	flex: 1;
`;
