import styled from 'styled-components';

export const WrapperStyled = styled.div`
	display: flex;
	flex-direction: column;
	border-radius: 36px;
`;

export const TitleStyled = styled.h2`
	margin: 0 0 16px 0;
	color: #ffffff;
	font-weight: 600;
	font-size: 24px;
`;

export const ContentWrapperStyled = styled.ul`
	gap: 12px;
	display: flex;
	flex: 1;
	flex-direction: column;
	overflow-y: auto;
	margin: 0;
	padding: 0;
	max-height: 400px;
	list-style: none;
`;

export const TransactionItemStyled = styled.li`
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16px;
	border: 1px solid rgba(255, 255, 255, 0.1);
	border-radius: 8px;
	background: rgba(255, 255, 255, 0.1);
	color: #ffffff;
	transition: all 0.2s ease;
`;

export const TransactionTextStyled = styled.span`
	font-weight: 500;
	font-size: 14px;
`;

export const TransactionAmountStyled = styled.span<{ isPositive: boolean }>`
	color: ${props => (props.isPositive ? '#81e82f' : '#fb2f32')};
	font-weight: 600;
	font-size: 14px;
`;
